import { defineStore } from 'pinia'

export interface AppState {
  theme: 'light' | 'dark'
  currentLocale: string
  isLoading: boolean
  notifications: Notification[]
}

export interface Notification {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message: string
  duration?: number
}

export const useAppStore = defineStore('app', {
  state: (): AppState => ({
    theme: 'light',
    currentLocale: 'zh-TW',
    isLoading: false,
    notifications: []
  }),

  getters: {
    isDarkMode: (state) => state.theme === 'dark',
    hasNotifications: (state) => state.notifications.length > 0
  },

  actions: {
    setTheme(theme: 'light' | 'dark') {
      this.theme = theme
      // Persist to localStorage
      if (process.client) {
        localStorage.setItem('theme', theme)
      }
    },

    setLocale(locale: string) {
      this.currentLocale = locale
    },

    setLoading(loading: boolean) {
      this.isLoading = loading
    },

    addNotification(notification: Omit<Notification, 'id'>) {
      const id = Date.now().toString()
      const newNotification: Notification = {
        id,
        duration: 5000,
        ...notification
      }
      
      this.notifications.push(newNotification)

      // Auto remove after duration
      if (newNotification.duration && newNotification.duration > 0) {
        setTimeout(() => {
          this.removeNotification(id)
        }, newNotification.duration)
      }
    },

    removeNotification(id: string) {
      const index = this.notifications.findIndex(n => n.id === id)
      if (index > -1) {
        this.notifications.splice(index, 1)
      }
    },

    clearNotifications() {
      this.notifications = []
    },

    // Initialize app state
    initialize() {
      if (process.client) {
        // Load theme from localStorage
        const savedTheme = localStorage.getItem('theme') as 'light' | 'dark'
        if (savedTheme) {
          this.theme = savedTheme
        }
      }
    }
  }
})
