import { defineStore } from 'pinia'

export interface FeatureFlags {
  // MVP Phase features
  enableProfessionalMode: boolean
  enableAdvancedSearch: boolean
  enableUserComments: boolean
  enableDownloads: boolean
  
  // Phase 2 features (disabled by default)
  enableUserAccounts: boolean
  enableBookmarks: boolean
  enableNotifications: boolean
  enableAnalytics: boolean
  
  // Content features
  enableVideoContent: boolean
  enableInteractiveContent: boolean
  enableMultimediaGallery: boolean
  
  // UI/UX features
  enableDarkMode: boolean
  enableAccessibilityEnhancements: boolean
  enableMobileOptimizations: boolean
}

export const useFeatureFlagsStore = defineStore('featureFlags', {
  state: (): FeatureFlags => ({
    // MVP Phase - enabled
    enableProfessionalMode: true,
    enableAdvancedSearch: false, // Will be enabled in Sprint 2
    enableUserComments: false,
    enableDownloads: true,
    
    // Phase 2 - disabled for MVP
    enableUserAccounts: false,
    enableBookmarks: false,
    enableNotifications: false,
    enableAnalytics: true, // Basic analytics for MVP
    
    // Content features
    enableVideoContent: false,
    enableInteractiveContent: false,
    enableMultimediaGallery: false,
    
    // UI/UX features
    enableDarkMode: false, // Will be added later
    enableAccessibilityEnhancements: true,
    enableMobileOptimizations: true
  }),

  getters: {
    // Convenience getters for common feature combinations
    isPhase2Enabled: (state) => 
      state.enableUserAccounts || state.enableBookmarks || state.enableNotifications,
    
    isContentEnhanced: (state) => 
      state.enableVideoContent || state.enableInteractiveContent || state.enableMultimediaGallery,
    
    isMVPReady: (state) => 
      state.enableProfessionalMode && state.enableDownloads && state.enableAccessibilityEnhancements
  },

  actions: {
    // Toggle individual features
    toggleFeature(feature: keyof FeatureFlags) {
      this[feature] = !this[feature]
    },

    // Enable/disable feature groups
    enablePhase2Features() {
      this.enableUserAccounts = true
      this.enableBookmarks = true
      this.enableNotifications = true
    },

    disablePhase2Features() {
      this.enableUserAccounts = false
      this.enableBookmarks = false
      this.enableNotifications = false
    },

    enableContentFeatures() {
      this.enableVideoContent = true
      this.enableInteractiveContent = true
      this.enableMultimediaGallery = true
    },

    // Load feature flags from environment or API
    async loadFeatureFlags() {
      // In production, this could load from an API or environment variables
      if (process.client) {
        try {
          const savedFlags = localStorage.getItem('featureFlags')
          if (savedFlags) {
            const parsed = JSON.parse(savedFlags)
            Object.assign(this, parsed)
          }
        } catch (error) {
          console.warn('Failed to load feature flags from localStorage:', error)
        }
      }
    },

    // Save feature flags (for development/testing)
    saveFeatureFlags() {
      if (process.client) {
        localStorage.setItem('featureFlags', JSON.stringify(this.$state))
      }
    }
  }
})
