<template>
  <footer class="bg-gray-50 border-t border-gray-200">
    <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
      <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
        <!-- Brand -->
        <div class="col-span-1">
          <h3 class="text-lg font-semibold text-gray-900 mb-4">
            {{ $t('hero.title') }}
          </h3>
          <p class="text-gray-600 text-sm">
            {{ $t('hero.subtitle') }}
          </p>
        </div>

        <!-- Quick Links -->
        <div class="col-span-1">
          <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4">
            Quick Links
          </h4>
          <ul class="space-y-2">
            <li v-for="item in quickLinks" :key="item.name">
              <NuxtLink
                :to="localePath(item.href)"
                class="text-gray-600 hover:text-gray-900 text-sm transition-colors"
              >
                {{ $t(item.name) }}
              </NuxtLink>
            </li>
          </ul>
        </div>

        <!-- Contact Info -->
        <div class="col-span-1">
          <h4 class="text-sm font-semibold text-gray-900 uppercase tracking-wider mb-4">
            {{ $t('nav.contact') }}
          </h4>
          <div class="text-gray-600 text-sm space-y-2">
            <p>Email: <EMAIL></p>
            <p>Phone: +886-2-1234-5678</p>
          </div>
        </div>
      </div>

      <!-- Bottom Section -->
      <div class="mt-8 pt-8 border-t border-gray-200">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          <p class="text-gray-500 text-sm">
            {{ $t('footer.copyright') }}
          </p>
          <p class="text-gray-500 text-xs max-w-md text-center md:text-right">
            {{ $t('footer.disclaimer') }}
          </p>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup lang="ts">
const localePath = useLocalePath()

const quickLinks = [
  { name: 'nav.about', href: '/about' },
  { name: 'nav.cancers', href: '/cancers' },
  { name: 'nav.faq', href: '/faq' },
  { name: 'nav.contact', href: '/contact' }
]
</script>
