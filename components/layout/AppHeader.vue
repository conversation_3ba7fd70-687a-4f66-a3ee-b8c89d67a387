<template>
  <header class="bg-white shadow-sm border-b border-gray-200">
    <nav class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8" aria-label="Main navigation">
      <div class="flex justify-between items-center h-16">
        <!-- Logo -->
        <div class="flex-shrink-0">
          <NuxtLink
            :to="localePath('/')"
            class="text-xl font-bold text-primary-600 hover:text-primary-700 transition-colors"
          >
            {{ $t('hero.title') }}
          </NuxtLink>
        </div>

        <!-- Desktop Navigation -->
        <div class="hidden md:block">
          <div class="ml-10 flex items-baseline space-x-4">
            <NuxtLink
              v-for="item in navigation"
              :key="item.name"
              :to="localePath(item.href)"
              class="px-3 py-2 rounded-md text-sm font-medium transition-colors"
              :class="[
                isCurrentRoute(item.href)
                  ? 'bg-primary-100 text-primary-700'
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              ]"
            >
              {{ $t(item.name) }}
            </NuxtLink>
          </div>
        </div>

        <!-- Language Switcher & Mobile Menu Button -->
        <div class="flex items-center space-x-4">
          <LangSwitcher />
          
          <!-- Mobile menu button -->
          <button
            type="button"
            class="md:hidden inline-flex items-center justify-center p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
            :aria-label="$t('accessibility.menu')"
            @click="mobileMenuOpen = !mobileMenuOpen"
          >
            <svg class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path v-if="!mobileMenuOpen" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
              <path v-else stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
      </div>

      <!-- Mobile Navigation -->
      <div v-show="mobileMenuOpen" class="md:hidden">
        <div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 border-t border-gray-200">
          <NuxtLink
            v-for="item in navigation"
            :key="item.name"
            :to="localePath(item.href)"
            class="block px-3 py-2 rounded-md text-base font-medium transition-colors"
            :class="[
              isCurrentRoute(item.href)
                ? 'bg-primary-100 text-primary-700'
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            ]"
            @click="mobileMenuOpen = false"
          >
            {{ $t(item.name) }}
          </NuxtLink>
        </div>
      </div>
    </nav>
  </header>
</template>

<script setup lang="ts">
const localePath = useLocalePath()
const route = useRoute()

const mobileMenuOpen = ref(false)

const navigation = [
  { name: 'nav.home', href: '/' },
  { name: 'nav.about', href: '/about' },
  { name: 'nav.cancers', href: '/cancers' },
  { name: 'nav.faq', href: '/faq' },
  { name: 'nav.contact', href: '/contact' },
  { name: 'nav.pro', href: '/pro' }
]

const isCurrentRoute = (href: string) => {
  return route.path === localePath(href)
}
</script>
