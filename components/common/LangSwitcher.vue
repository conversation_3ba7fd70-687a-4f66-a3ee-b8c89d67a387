<template>
  <div class="relative">
    <select
      v-model="currentLocale"
      :aria-label="$t('accessibility.languageSwitch')"
      class="appearance-none bg-white border border-gray-300 rounded-md px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
      @change="switchLanguage"
    >
      <option
        v-for="locale in availableLocales"
        :key="locale.code"
        :value="locale.code"
      >
        {{ locale.name }}
      </option>
    </select>
    <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
      <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
      </svg>
    </div>
  </div>
</template>

<script setup lang="ts">
const { locale, locales } = useI18n()
const localePath = useLocalePath()
const router = useRouter()

const currentLocale = ref(locale.value)
const availableLocales = computed(() => locales.value)

const switchLanguage = async () => {
  await navigateTo(localePath(router.currentRoute.value.path, currentLocale.value))
}
</script>
