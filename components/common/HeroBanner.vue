<template>
  <section class="bg-gradient-to-r from-primary-600 to-primary-700 text-white">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-24">
      <div class="text-center">
        <h1 class="text-4xl md:text-6xl font-bold mb-6">
          <slot name="title">
            {{ title }}
          </slot>
        </h1>
        
        <p class="text-xl md:text-2xl mb-8 text-primary-100 max-w-3xl mx-auto">
          <slot name="subtitle">
            {{ subtitle }}
          </slot>
        </p>
        
        <div v-if="showCta" class="flex flex-col sm:flex-row gap-4 justify-center">
          <slot name="cta">
            <NuxtLink
              v-if="ctaLink"
              :to="localePath(ctaLink)"
              class="btn-primary inline-flex items-center px-8 py-3 text-lg font-medium rounded-lg transition-all hover:bg-white hover:text-primary-600 focus:ring-4 focus:ring-white focus:ring-opacity-50"
            >
              {{ ctaText }}
              <svg class="ml-2 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </NuxtLink>
          </slot>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
interface Props {
  title?: string
  subtitle?: string
  ctaText?: string
  ctaLink?: string
  showCta?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '',
  subtitle: '',
  ctaText: '',
  ctaLink: '',
  showCta: true
})

const localePath = useLocalePath()
</script>
