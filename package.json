{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "prepare": "husky", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "format:check": "prettier --check ."}, "lint-staged": {"**/*.{ts,js,vue,json,md}": ["eslint --fix", "prettier --write"]}, "dependencies": {"@nuxtjs/i18n": "^10.0.5", "@pinia/nuxt": "^0.11.2", "nuxt": "^4.0.3", "pinia": "^3.0.3", "vue": "^3.5.18", "vue-router": "^4.5.1"}, "devDependencies": {"@types/node": "^24.3.0", "@vue/eslint-config-typescript": "^14.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.33.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-vue": "^10.4.0", "husky": "^9.1.7", "lint-staged": "^16.1.5", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.12", "typescript": "^5.9.2"}}