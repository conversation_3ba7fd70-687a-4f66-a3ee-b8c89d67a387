// https://nuxt.com/docs/api/configuration/nuxt-config
export default defineNuxtConfig({
  compatibilityDate: '2025-07-15',
  devtools: { enabled: true },

  modules: [
    '@pinia/nuxt',
    '@nuxtjs/i18n'
  ],

  css: ['./assets/css/tailwind.css'],

  postcss: {
    plugins: {
      '@tailwindcss/postcss': {},
      autoprefixer: {}
    }
  },

  i18n: {
    locales: ['en', 'zh-TW'],
    defaultLocale: 'zh-TW',
    strategy: 'prefix_except_default'
  }
})
