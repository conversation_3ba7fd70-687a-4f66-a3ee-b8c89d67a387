<template>
  <div>
    <!-- Hero Section -->
    <HeroBanner
      :title="$t('hero.title')"
      :subtitle="$t('hero.subtitle')"
      :cta-text="$t('hero.cta')"
      cta-link="/about"
    />

    <!-- Content Sections -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
      <!-- Introduction Section -->
      <section class="mb-16">
        <div class="text-center mb-12">
          <h2 class="text-3xl font-bold text-gray-900 mb-4">
            {{ $t('nav.about') }}
          </h2>
          <p class="text-lg text-gray-600 max-w-3xl mx-auto">
            提供專業、可信的癌症熱治療資訊，幫助患者和醫療專業人員了解這項治療方式。
          </p>
        </div>
      </section>

      <!-- Quick Access Cards -->
      <section class="mb-16">
        <h2 class="text-2xl font-bold text-gray-900 mb-8 text-center">
          快速導覽
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div
            v-for="card in quickAccessCards"
            :key="card.title"
            class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow p-6 border border-gray-200"
          >
            <div class="flex items-center mb-4">
              <div class="w-12 h-12 bg-primary-100 rounded-lg flex items-center justify-center">
                <svg class="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" :d="card.icon" />
                </svg>
              </div>
              <h3 class="ml-4 text-lg font-semibold text-gray-900">
                {{ card.title }}
              </h3>
            </div>
            <p class="text-gray-600 mb-4">
              {{ card.description }}
            </p>
            <NuxtLink
              :to="localePath(card.link)"
              class="text-primary-600 hover:text-primary-700 font-medium inline-flex items-center"
            >
              {{ $t('common.learnMore') }}
              <svg class="ml-1 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </NuxtLink>
          </div>
        </div>
      </section>

      <!-- CTA Section -->
      <section class="bg-gray-50 rounded-lg p-8 text-center">
        <h2 class="text-2xl font-bold text-gray-900 mb-4">
          需要更多資訊？
        </h2>
        <p class="text-gray-600 mb-6">
          我們的專業團隊隨時為您提供協助和諮詢。
        </p>
        <NuxtLink
          :to="localePath('/contact')"
          class="btn-primary"
        >
          {{ $t('nav.contact') }}
        </NuxtLink>
      </section>
    </div>
  </div>
</template>

<script setup lang="ts">
const localePath = useLocalePath()

// SEO Meta
useHead({
  title: 'Cancer Hyperthermia Information Hub',
  meta: [
    { name: 'description', content: '提供專業、可信的癌症熱治療資訊，幫助患者和醫療專業人員了解這項治療方式。' }
  ]
})

const quickAccessCards = [
  {
    title: '癌症類型',
    description: '了解不同癌症類型的熱治療應用',
    icon: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
    link: '/cancers'
  },
  {
    title: '常見問題',
    description: '查看患者和醫療人員最常詢問的問題',
    icon: 'M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z',
    link: '/faq'
  },
  {
    title: '專業版',
    description: '醫療專業人員專用的詳細資訊',
    icon: 'M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253',
    link: '/pro'
  }
]
</script>
